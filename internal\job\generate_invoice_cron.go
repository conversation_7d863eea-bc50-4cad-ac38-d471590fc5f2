package job

import (
	"context"
	"encoding/json"
	"github.com/Norray/xrocket/xtool"
	"time"

	"github.com/Norray/medic-crew/internal/task"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xamqp"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const CronCheckJobForInvoice = "cron_check_job_for_invoice" // 檢查需要生成發票的工作

// 生成發票草稿定時任務 - 每分鐘執行
func CheckJobForInvoice() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).With<PERSON>ield("task", CronCheckJobForInvoice)

	logger.Info("Start CheckJobForInvoice task")

	db := xgorm.DB.WithContext(ctx)

	// 檢查定時任務是否應該運行
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckJobForInvoice)
	if err != nil {
		logger.Errorf("[CRON] fail to check job for invoice task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckJobForInvoice)
		return
	}
	nowTime := time.Now().UTC().Truncate(time.Second)
	// 1小時前
	startTime := nowTime.Add(-1 * time.Hour)

	// 查詢符合條件的工作ID：
	// 1. 工作付款條款為預付
	// 2. 工作開始時間在1小時後
	// 3. 有工作申請記錄為已接受且未生成發票
	var jobIds []uint64
	err = db.Table("job as j").
		Select("DISTINCT j.id").
		Joins("JOIN job_application as ja ON j.id = ja.job_id").
		Where("j.payment_terms = ?", model.JobPaymentTermsPayUpfront).
		Where("j.begin_time <= %s", startTime.Format(xtool.DateTimeSecA1)).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("ja.invoice_generated = ?", model.JobApplicationInvoiceGeneratedN).
		Limit(30). // 每次處理限制數量，避免一次處理太多
		Pluck("j.id", &jobIds).Error

	if err != nil {
		logger.Errorf("Fail to query jobs: %v", err)
		return
	}

	logger.Infof("Found %d jobs need to generate invoice", len(jobIds))

	// 將每個符合條件的工作發送到隊列
	for _, jobId := range jobIds {
		req := task.CreateInvoiceDraftReq{
			JobId: jobId,
		}
		var reqData []byte
		reqData, err = json.Marshal(req)
		if err != nil {
			logger.Errorf("Fail to marshal request: %v, jobId: %d", err, jobId)
			continue
		}

		// 發送到隊列
		err = xamqp.SendTask(task.CreateInvoiceDraftTask, xamqp.Task{
			MessageId: task.CreateInvoiceDraftTask,
			TaskId:    task.CreateInvoiceDraftTask + "_" + uuid.NewV4().String(),
			Data:      string(reqData),
		})
		if err != nil {
			logger.Errorf("Fail to send task: %v, jobId: %d", err, jobId)
			continue
		}

		logger.Infof("Send create invoice task for jobId: %d", jobId)
	}

	logger.Info("CheckJobForInvoice task completed")
}
