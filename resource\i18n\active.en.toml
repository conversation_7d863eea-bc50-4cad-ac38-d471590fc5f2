"" = "Unable to sign in this account."
"alerter.google_oauth.can_not_found_state" = "Authorization timed out, please reauthorize."
"alerter.job.already_apply" = "You have already applied for this job."
"alerter.job.expired" = "The job has expired."
"alerter.job.full" = "This job is full."
"alerter.job.not_published" = "This job is not published."
"alerter.job.not_recruiting" = "This job is not recruiting."
"alerter.refresh_token.can_not_found" = "Authorization timed out, please reauthorize."
"casbin.authentication.forbidden" = "Permission denied."
"change_password.failed" = "Old password is incorrect."
"checker.action.code.already_exists" = "The action code is already exists. Please try another code."
"checker.action.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.agreement.id.does_not_exist" = "No such record, please try after reloading."
"checker.allowance.id.does_not_exist" = "No such record, please try after reloading."
"checker.benefit.id.does_not_exist" = "No such record, please try after reloading."
"checker.casbin.id.does_not_exist" = "Policy does not exist."
"checker.casbin.police.already_exists" = "Police already exists."
"checker.commission.id.does_not_exist" = "No such record, please try after reloading."
"checker.confirmation_note.cannot_modify" = "This confirmation note cannot be modified in current status."
"checker.confirmation_note.cannot_review" = "This confirmation note cannot be reviewed in current status."
"checker.confirmation_note.cannot_submit" = "This confirmation note cannot be submitted in current status."
"checker.confirmation_note.cannot_void" = "This confirmation note cannot be voided in current status."
"checker.confirmation_note.gst.no_access" = "No access for GST."
"checker.confirmation_note.id.does_not_exist" = "No such record, please try after reloading."
"checker.confirmation_note.invalid_amount_summary" = "Invalid amount summary."
"checker.confirmation_note.item.allowance_amount_invalid" = "Invalid allowance amount submitted."
"checker.confirmation_note.item.allowance_duplicate" = "Allowance duplicate."
"checker.confirmation_note.item.allowance_invalid" = "Invalid allowance data submitted."
"checker.confirmation_note.item.allowance_not_found" = "Allowance not found."
"checker.confirmation_note.item.allowance_super_amount_invalid" = "Invalid allowance super amount submitted."
"checker.confirmation_note.item.invalid" = "Invalid item data submitted."
"checker.confirmation_note.not_creator" = "You are not the creator of this confirmation note."
"checker.department.id.does_not_exist" = "No such record, please try after reloading."
"checker.department.is_used" = "This department is currently in use and cannot be deleted."
"checker.document.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.document_file.code.does_not_exist" = "Some thing went wrong, please try again later."
"checker.document_file.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.document_file.ids.does_not_exist" = "Some thing went wrong, please try again later."
"checker.facility.id.does_not_exist" = "No such record, please try after reloading."
"checker.facility_agreement.cannot_modify" = "This agreement cannot be modified in current status."
"checker.facility_agreement.cannot_send_agreement_email" = "This agreement cannot be sent to the facility."
"checker.facility_agreement.cannot_sign_agreement" = "This agreement cannot be signed."
"checker.facility_agreement.has_unsigned_agreement" = "Please sign all pending agreements before proceeding."
"checker.facility_agreement.id.does_not_exist" = "No such record, please try after reloading."
"checker.facility_agreement.invalid_time_range" = "Begin time must be earlier than end time."
"checker.facility_agreement.no_valid_agreement" = "No valid agreement for the job time period."
"checker.facility_agreement.schedule_time_out_of_range" = "Schedule time must be within agreement time range."
"checker.facility_agreement.time_range_overlap" = "The validity period of the agreement overlaps with that of an existing active agreement."
"checker.facility_blacklist.already_in_blacklist" = "The professional is already in the blacklist."
"checker.facility_blacklist.id.does_not_exist" = "No such record, please try after reloading."
"checker.facility_file.code.does_not_exist" = "Some thing went wrong, please try again later."
"checker.facility_file.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.facility_file.id.professional.does_not_exist" = "No such file, please try again later."
"checker.facility_file.ids.does_not_exist" = "Some thing went wrong, please try again later."
"checker.facility_file.orientation_documentation.not_found" = "Some thing went wrong, please try again later."
"checker.facility_profile.agreement_id.correct" = "Some thing went wrong, please try again later."
"checker.facility_profile.already_init" = "Some thing went wrong, please try again later."
"checker.facility_profile.can_not_approve" = "Some thing went wrong, please try again later."
"checker.facility_profile.can_not_edit" = "Some thing went wrong, please try again later."
"checker.facility_profile.can_not_submit_or_un_submit" = "Some thing went wrong, please try again later."
"checker.facility_profile.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.facility_profile.information_incomplete" = "Please check if all the required fields on the page have been completed."
"checker.facility_profile.public_liability_insurance.expired" = "The Public Liability Insurance document has expired."
"checker.facility_specialisation.codes.invalid" = "One or more codes are invalid for the specified position."
"checker.facility_specialisation.codes.valid" = "All codes are valid for the specified position."
"checker.facility_specialisation.position.invalid" = "The specified position is not supported."
"checker.faq.id.does_not_exist" = "No such record, please try after reloading."
"checker.faq_file.code.does_not_exist" = "Some thing went wrong, please try again later."
"checker.faq_file.uuid.does_not_exist" = "Some thing went wrong, please try again later."
"checker.file.code.does_not_exist" = "Some thing went wrong, please try again later."
"checker.file.size.too_large" = "File size exceeds the limit of {{.MaxSize}} MB."
"checker.file.type.not_allowed" = "File type {{.FileType}} is not allowed. Allowed types: {{.AllowedTypes}}"
"checker.hourly_rate.id.does_not_exist" = "No such record, please try after reloading."
"checker.invoice.admin.edit_sent_document" = "The document has been sent and cannot be edited."
"checker.invoice.invalid_amount_summary" = "Invalid amount summary."
"checker.invoice.invalid_payment_status" = "Invalid payment status."
"checker.job.cannot_accept.time_has_job" = "You already have another job scheduled during this time period."
"checker.job.cannot_delete" = "The job cannot be deleted."
"checker.job.cannot_delete.application" = "The job has applicants, cannot be deleted."
"checker.job.cannot_invite.accept" = "The professional has already accepted the invitation."
"checker.job.cannot_invite.application_cancel" = "You have already canceled the application."
"checker.job.cannot_invite.begin_time" = "This job has already started."
"checker.job.cannot_invite.facility_cancel" = "You have already canceled the application."
"checker.job.cannot_invite.invite" = "You have already sent an invitation to this job."
"checker.job.cannot_invite.professional_cancel" = "The professional has already canceled the application."
"checker.job.cannot_invite.publish" = "This job is not published."
"checker.job.cannot_invite.publish_time" = "The job has not been published yet."
"checker.job.cannot_invite.will_start" = "This job will start soon."
"checker.job.cannot_invite.withdraw" = "The professional has already withdrawn the invitation."
"checker.job.cannot_revoke_invite.accept" = "The professional has already accepted the invitation."
"checker.job.cannot_revoke_invite.decline" = "The professional has already declined the invitation."
"checker.job.cannot_revoke_invite.other" = "The invitation cannot be revoked."
"checker.job.cannot_withdraw.status" = "This job application is not in an invitation status."
"checker.job.grand_total_mismatch" = "The calculated grand total does not match the provided amount."
"checker.job.hiring_ended" = "The job hiring has ended."
"checker.job.id.does_not_exist" = "No such record, please try after reloading."
"checker.job.invite_max" = "The job has reached the maximum number of people."
"checker.job.no_available" = "The job is not available."
"checker.job.not_published" = "The job is not published."
"checker.job.required_max" = "The job has reached the maximum number of people."
"checker.job.shift_allocation.cannot_edit" = "The job shift allocation cannot be edited."
"checker.job.shift_time.duration_too_long" = "Shift duration cannot exceed 24 hours."
"checker.job.shift_time.duration_too_short" = "Shift duration must be at least 2 hours."
"checker.job.status.cannot_edit" = "The job status is not editable."
"checker.job.status.cannot_publish" = "The job status is not publishable."
"checker.job.status.cannot_publish.begin_time.greater_than_now" = "The job begin time must be greater than the current time by 1 hour."
"checker.job.status.cannot_publish.begin_time.required" = "The job begin time is required."
"checker.job.status.cannot_publish.shift_time_range" = "The job shift time range is not valid."
"checker.job.status.cannot_publish.timezone" = "Please select the service address information."
"checker.job.status.cannot_update" = "The job status is not updatable."
"checker.job.status.cannot_update.begin_time" = "The job is about to start, cannot be updated."
"checker.job.status.cannot_update.complete" = "The job has completed, cannot be updated."
"checker.job.will_start" = "The job is about to start"
"checker.job_application.cannot_cancel" = "The job application cannot be cancelled."
"checker.job_application.cannot_withdraw.invite" = "You have been invited to join a job by the facility. Please respond to the invitation first."
"checker.job_application.cannot_withdraw.status" = "The job application cannot be withdrawn."
"checker.job_application.id.does_not_exist" = "No such application record, please try after reloading."
"checker.job_file.facility_files.invalid_for_profession" = "Some files do not exist or are not valid for the specified profession."
"checker.job_file.profession.not_supported" = "The specified profession is not supported."
"checker.job_schedule.allowance.not_exist" = "Some allowances do not exist."
"checker.job_schedule.cannot_delete.published" = "The schedule has published jobs and cannot be deleted."
"checker.job_schedule.id.delete.does_not_exist" = "No such schedule record, please try after reloading."
"checker.job_schedule.id.does_not_exist" = "The schedule does not exist."
"checker.job_schedule.shift_time.duration_too_long" = "Shift duration cannot exceed 24 hours."
"checker.job_schedule.shift_time.duration_too_short" = "Shift duration must be at least 2 hours."
"checker.job_schedule.shift_time.empty" = "Job shift items cannot be empty."
"checker.job_schedule.shift_time.invalid_begin_time" = "Invalid begin time format."
"checker.job_schedule.shift_time.invalid_end_time" = "Invalid end time format."
"checker.job_schedule.shift_time.invalid_range" = "End time must be after begin time."
"checker.job_schedule.status.has_published_job" = "The completed publication schedule cannot be stopped."
"checker.job_schedule.status.same" = "The schedule status is already set to the target status."
"checker.job_shift_time.id.can_not_select" = "Some thing went wrong, please try again later."
"checker.location.id.cannot_be_parent" = "The parent location is incorrect."
"checker.location.id.cannot_delete" = "The location cannot be deleted."
"checker.location.id.does_not_exist" = "No such record, please try after reloading."
"checker.menu.code.already_exists" = "The menu code is already exists. Please try another code."
"checker.menu.id.cannot_be_deleted" = "This menu cannot be deleted."
"checker.menu.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.professional.abn_cancelled" = "The ABN is cancelled."
"checker.professional.abn_invalid" = "The ABN is invalid."
"checker.professional.already_init" = "Some thing went wrong, please try again later."
"checker.professional.already_submitted" = "The profile has already been submitted."
"checker.professional.can_edit" = "The current status cannot be edited."
"checker.professional.can_not_approve" = "Some thing went wrong, please try again later."
"checker.professional.can_submit" = "The profile is not complete, please fill in completely."
"checker.professional.can_withdraw" = "The profile cannot be withdrawn."
"checker.professional.experience_level_invalid" = "Experience level does not match qualification end date."
"checker.professional.file_expiration_invalid" = "{{.Name}} expiration date is invalid."
"checker.professional.file_expiration_invalid.criminal" = "The National Criminal Check Expiry Date must be at least 3 years ago."
"checker.professional.file_expiration_invalid.passport" = "The passport must be valid or expired within 2 years."
"checker.professional.file_expiration_invalid.today" = "{{.Name}} has expired"
"checker.professional.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.professional.id_check_file_types_invalid" = "ID Check file types do not match uploaded files."
"checker.professional.missing_update_prompt" = "Please fill in the update prompt."
"checker.professional.preferred_grade_invalid" = "Preferred Grade does not match Experience Level."
"checker.professional.preferred_specialities" = "The preferred specialities is not valid."
"checker.professional.references_pending_approval" = "Referee information pending approval. Please review and approve the referee details first."
"checker.professional.working_with_children_states_mismatch" = "Working with children/vulnerable people states do not match file descriptions."
"checker.professional_file.edit.missing_parameter" = "Some thing went wrong, please try again later."
"checker.professional_file.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.professional_file.id.facility.does_not_exist" = "No such file, please try again later."
"checker.professional_superannuation.declaration.not_confirmed" = "Declaration must be confirmed when submitting superannuation information."
"checker.professional_superannuation.id.does_not_exist" = "No such record, please try after reloading."
"checker.recaptcha.check_failed" = "reCAPTCHA verification failed"
"checker.register.code.expired" = "The verification code has expired. Please resend the verification code."
"checker.register.code.send.count.limit" = "The registration request has timed out. Please register again."
"checker.register.info.not_exists" = "The registration request has timed out. Please register again."
"checker.register.user.email.already_exists" = "Email already registered. Please try another email."
"checker.register.verification.code.invalid" = "Invalid verification code."
"checker.role.action.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.role.id.cannot_delete" = "This role cannot be deleted."
"checker.role.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.role.name.already_exists" = "Some thing went wrong, please try again later."
"checker.selection.does_not_exist" = "Some thing went wrong, please try again later."
"checker.service_location.id.does_not_exist" = "No such record, please try after reloading."
"checker.training.module.access_denied" = "Access denied. Please complete the previous module first."
"checker.training.module.already_completed" = "All questions in this module have been completed correctly."
"checker.training.module.incomplete_answers" = "Please answer all questions in this module before submitting."
"checker.training.module.not_found" = "Training module not found."
"checker.training.module.prev_not_found" = "Previous training module not found."
"checker.training.module.professional.all.not_completed" = "Please complete all training first."
"checker.training.video.not_watched" = "Please watch the training video first."
"checker.user.email.already_exists" = "Email already exists."
"checker.user.email.does_not_exist" = "This email address is not registered."
"checker.user.id.cannot_delete" = "This user cannot be deleted."
"checker.user.id.does_not_exist" = "Some thing went wrong, please try again later."
"checker.ws_message.id.check_message_failed" = "Some thing went wrong, please try again later."
"checker.ws_message.id.facility_cannot_send_to_professional" = "cannot send messages to professional."
"checker.ws_message.id.handle_message_failed" = "Some thing went wrong, please try again later."
"checker.ws_message.id.message_already_processed" = "Message already processed."
"checker.ws_message.id.message_not_found" = "Message not found."
"checker.ws_message.id.message_type_not_allowed" = "Message type not allowed."
"checker.ws_message.id.professional_cannot_send_to_facility" = "cannot send messages to facility."
"checker.ws_message.id.required_param_missing" = "Required parameter missing."
"checker.ws_message.id.session_not_found" = "Session not found."
"checker.ws_message.id.unmarshal_message_failed" = "Some thing went wrong, please try again later."
"email.agreement_expired.button" = "Click here to Renew Agreement"
"email.agreement_expired.content" = "This is to inform you that your agreement with Medic Crew has expired. To continue posting jobs and using our platform, please log in to your account to sign a new agreement."
"email.agreement_expired.greeting" = "Hi {{.FacilityName}}"
"email.agreement_expired.signature" = "Thank you"
"email.agreement_expired.subject" = "Action Required: Your Agreement has Expired"
"email.agreement_ready.button" = "Click here to Review Agreement"
"email.agreement_ready.content" = "We have reviewed the information provided by your facility and prepared a draft agreement accordingly.\n\nTo proceed, please log in to Medic Crew to complete your facility details and sign the agreement. You can use the same login credentials (email and password) created during your initial registration.\n\nIf you have any questions or need assistance, please don't hesitate to contact us."
"email.agreement_ready.greeting" = "Hi {{.FacilityName}}"
"email.agreement_ready.signature" = "Thank you"
"email.agreement_ready.subject" = "Agreement Ready for Review and Completion on Medic Crew"
"email.agreement_renewal_reminder.button" = "Click here to Renew Agreement"
"email.agreement_renewal_reminder.content" = "This is a friendly reminder that the agreement between your facility and Medic Crew is set to expire in one month. To ensure continued service without interruption, please log in to your account and finalise a new agreement at your earliest convenience."
"email.agreement_renewal_reminder.greeting" = "Hi {{.FacilityName}}"
"email.agreement_renewal_reminder.signature" = "Thank you"
"email.agreement_renewal_reminder.subject" = "Medic Crew Agreement Renewal Reminder"
"email.facility_approved.button" = "Click here to Login"
"email.facility_approved.content" = "We are pleased to inform you that we have reviewed and approved the detailed information you provided for your facility. You can now log in to your account and start posting jobs to find the right professionals for your facility."
"email.facility_approved.greeting" = "Hi {{.FacilityName}}"
"email.facility_approved.signature" = "Thank you"
"email.facility_approved.subject" = "Your Facility's Information Has Been Approved"
"email.facility_job_cancellation.button" = "Click here to view jobs"
"email.facility_job_cancellation.content" = "This is to inform you that the job/shift you were scheduled for at {{.FacilityName}} has been cancelled.\n\nWe apologise for any inconvenience this may cause and thank you for your understanding.\nWe recommend checking for other available jobs on the platform."
"email.facility_job_cancellation.greeting" = "Hi {{.ProfessionalName}}"
"email.facility_job_cancellation.signature" = "Thank you"
"email.facility_job_cancellation.subject" = "Job Cancellation Notification"
"email.facility_rejected.button" = "Click here to Review Your Application"
"email.facility_rejected.content" = "We have reviewed the information you submitted for your facility, but we were unable to approve your account at this time. This may be due to incomplete or incorrect details. Please log in to your account to review and resubmit your information."
"email.facility_rejected.greeting" = "Hi {{.FacilityName}}"
"email.facility_rejected.signature" = "Thank you"
"email.facility_rejected.subject" = "Action Required: Your Application Needs Attention"
"email.help_desk_notification.button" = "Click here to Go To Task"
"email.help_desk_notification.content" = "A new help desk request has been submitted by {{.RequesterName}}.\nPlease log in to your admin account to review the request and provide a timely response."
"email.help_desk_notification.greeting" = "Hi"
"email.help_desk_notification.signature" = "Thank you"
"email.help_desk_notification.subject" = "Professional Shift Started"
"email.invoice_notification.button" = "Click here to view the invoice"
"email.invoice_notification.content" = "Your invoice has been generated. Please arrange payment at your earliest convenience to ensure the smooth continuation of services."
"email.invoice_notification.greeting" = "Hi {{.FacilityName}}"
"email.invoice_notification.signature" = "Thank you"
"email.invoice_notification.subject" = "Medic Crew Invoice"
"email.new_facility_application.button" = "Click here to Go To Task"
"email.new_facility_application.content" = "This is an automated notification to inform you that a new facility has submitted an application to join the Medic Crew platform.\n\nPlease log in to your admin account to review the application and verify the submitted details."
"email.new_facility_application.greeting" = "Hi"
"email.new_facility_application.signature" = "Thank you"
"email.new_facility_application.subject" = "New Facility Application Received"
"email.professional_approved.button" = "Click here to Login"
"email.professional_approved.content" = "We have reviewed and approved your professional profile on the Medic Crew platform. You are now ready to start applying for jobs that match your skills and experience! We wish you success in your job search."
"email.professional_approved.greeting" = "Hi {{.ProfessionalName}}"
"email.professional_approved.signature" = "Thank you"
"email.professional_approved.subject" = "Your Profile Has Been Approved!"
"email.professional_job_cancellation.button" = "Click here to view the shift"
"email.professional_job_cancellation.content" = "We wish to inform you that {{.ProfessionalName}} has had to cancel their upcoming shift at your facility.\n\nWe understand this may cause inconvenience. You may wish to review other applicants for the job on the platform to find a suitable replacement."
"email.professional_job_cancellation.greeting" = "Hi {{.FacilityName}}"
"email.professional_job_cancellation.signature" = "Thank you"
"email.professional_job_cancellation.subject" = "Job Cancellation Notification"
"email.professional_rejected.button" = "Click here to Review Your Application"
"email.professional_rejected.content" = "We have reviewed the detailed information you provided, but we were unable to approve your professional profile at this time. This may be due to incomplete or incorrect details. Please log in to your account to review and resubmit your information."
"email.professional_rejected.greeting" = "Hi {{.ProfessionalName}}"
"email.professional_rejected.signature" = "Thank you"
"email.professional_rejected.subject" = "Action Required: Your Profile Needs Attention"
"email.urgent_shift_unfilled.button" = "Click here to view the shift"
"email.urgent_shift_unfilled.content" = "This is a courtesy notification regarding the {{.Position}} shift scheduled to start at {{.StartTime}}. Despite our efforts, we have not been able to secure a professional for this shift and it is likely to remain unfilled.\n\nWe recommend preparing alternative arrangements on your end."
"email.urgent_shift_unfilled.greeting" = "Hi {{.FacilityName}}"
"email.urgent_shift_unfilled.signature" = "Thank you for your understanding"
"email.urgent_shift_unfilled.subject" = "Urgent: {{.Position}} Shift Starting at {{.StartTime}} Remains Unfilled"
"facility_agreement.email.button.text" = "Click here to Renew Agreement"
"facility_agreement.email.greeting" = "Hi"
"facility_agreement.email.intros" = "This is a friendly reminder that the agreement between your facility and Medic Crew is set to expire in one month. To ensure continued service without interruption, please log in to your account and finalise a new agreement at your earliest convenience."
"facility_agreement.email.outros" = "If you have any questions or need assistance, please don't hesitate to contact us."
"facility_agreement.email.signature" = "Thank you,"
"facility_agreement.email.subject" = "Agreement Ready for Review and Completion on Medic Crew"
"forget.password.email.button.text" = "RESET "
"forget.password.email.greeting" = "Hi"
"forget.password.email.instructions" = "Please click the button below to reset your password."
"forget.password.email.intros" = "We Receive your forgot your password message."
"forget.password.email.outros" = "Please reset your password within 30 minutes, re-apply is needed after expiration."
"forget.password.email.signature" = "Thank you very much!"
"forget.password.email.subject" = "Reset password"
"forget.password.email.trouble.text" = "If you have any problem to open the button '{ACTION}', please copy and paste the link below to your web browser."
"get.user.login.forget.password.fail" = "The reset password link you opened is invalid. Please re-enter your email address below and send it."
"job_application.session_not_found" = "Session not found"
"job_schedule.begin_date.invalid" = "Invalid start date format."
"job_schedule.daily_interval.required" = "Daily repeat interval must be greater than 0."
"job_schedule.date_range.invalid" = "End date cannot be earlier than start date."
"job_schedule.end_date.invalid" = "Invalid end date format."
"job_schedule.monthly_day_of_month.invalid" = "Day of month must be between 1 and 31."
"job_schedule.monthly_interval.required" = "Monthly repeat interval must be greater than 0."
"job_schedule.monthly_type.invalid" = "Invalid monthly repeat type."
"job_schedule.monthly_week_index.invalid" = "Week index must be between 1 and 5."
"job_schedule.no_valid_dates" = "No valid job dates will be generated with the current settings."
"job_schedule.repeat_type.invalid" = "Invalid repeat type."
"job_schedule.shift_time.begin_time.invalid" = "Invalid begin time format."
"job_schedule.shift_time.end_time.invalid" = "Invalid end time format."
"job_schedule.shift_time.overlap" = "Shift time overlaps."
"job_schedule.shift_time.required" = "Shift time is required."
"job_schedule.timezone.invalid" = "Invalid timezone."
"job_schedule.week_days.required" = "At least one weekday must be specified for weekly repeats."
"job_schedule.weekly_interval.required" = "Weekly repeat interval must be greater than 0."
"jwt.authentication.expired" = "Authentication expired."
"jwt.authentication.failed" = "Authentication failed."
"login.failed" = "Incorrect account or password."
"oauth_login.failed" = "Unable to sign in this account."
"professional.profile.field.abn" = "ABN"
"professional.profile.field.abn_number" = "ABN"
"professional.profile.field.additional_certification" = "Additional Certification"
"professional.profile.field.address" = "Residential Address"
"professional.profile.field.address_extra" = "Residential Address"
"professional.profile.field.ahpra_certificate" = "AHPRA Certificate"
"professional.profile.field.ahpra_expiry_date" = "AHPRA Expiry Date"
"professional.profile.field.ahpra_number" = "AHPRA No."
"professional.profile.field.australian_birth_certificate" = "Australian Birth Certificate"
"professional.profile.field.australian_citizenship_certificate" = "Australian Citizenship Certificate"
"professional.profile.field.australian_passport" = "Australian Passport"
"professional.profile.field.australian_public_service_employee_id_card" = "Australian Public Service Employee ID Card"
"professional.profile.field.centrelink_or_pension_card" = "Centrelink Or Pension Card"
"professional.profile.field.commonwealth_statutory_declaration" = "Commonwealth Statutory Declaration"
"professional.profile.field.completed_studies_in_last_three_years" = "Completed Studies In Last Three Years"
"professional.profile.field.credit_debit_atm_card" = "Credit Debit ATM Card"
"professional.profile.field.current_australia_driver_licence" = "Current Australia Driver Licence"
"professional.profile.field.current_immunisation_records" = "Current Immunisation Records"
"professional.profile.field.date_of_birth" = "Date Of Birth"
"professional.profile.field.disclosure" = "Disclosure"
"professional.profile.field.disclosure_questions" = "Disclosure Questions"
"professional.profile.field.distance_within" = "Distance Within"
"professional.profile.field.emergency_contact_first_name" = "Emergency Contact First Name"
"professional.profile.field.emergency_contact_last_name" = "Emergency Contact Last Name"
"professional.profile.field.emergency_contact_phone" = "Emergency Contact Phone"
"professional.profile.field.emergency_contact_relationship" = "Emergency Contact Relationship"
"professional.profile.field.experience_level" = "Experience Level"
"professional.profile.field.experiences" = "Experiences"
"professional.profile.field.first_name" = "First Name"
"professional.profile.field.foreign_passport" = "Foreign Passport"
"professional.profile.field.gender" = "Gender"
"professional.profile.field.graduation_institution" = "Graduation Institution"
"professional.profile.field.graduation_year" = "Year of Graduation"
"professional.profile.field.has_completed_infection_control_training" = "CPR/BLS Certification"
"professional.profile.field.has_overseas_citizenship_or_pr" = "Citizenship Since Age 16"
"professional.profile.field.has_personal_accident_illness_insurance" = "Personal Accident & Illness Insurance (Highly Recommended)"
"professional.profile.field.indemnity_insurance_certificate" = "Indemnity Insurance Certificate"
"professional.profile.field.institution_country" = "Country of Institution"
"professional.profile.field.language" = "Language Other Than English"
"professional.profile.field.last_name" = "Last Name"
"professional.profile.field.location_city" = "Residential Address"
"professional.profile.field.location_lat" = "Residential Address"
"professional.profile.field.location_lng" = "Residential Address"
"professional.profile.field.location_route" = "Residential Address"
"professional.profile.field.location_state" = "Residential Address"
"professional.profile.field.medicare_card" = "Medicare Card"
"professional.profile.field.medication_endorsement" = "Medication Endorsement"
"professional.profile.field.minimum_hourly_rate" = "Minimum Hourly Rate"
"professional.profile.field.national_criminal_check" = "National Criminal Check"
"professional.profile.field.other_australian_government_issue_id_card" = "Other Australian Government Issue ID Card"
"professional.profile.field.permission_to_work" = "Permission to work in Australia"
"professional.profile.field.personal_accident_illness_insurance_certificate" = "Personal Accident & Illness Insurance (Highly Recommended)"
"professional.profile.field.personal_care_worker_qualification_bachelor_nursing" = "Bachelor of Nursing"
"professional.profile.field.personal_care_worker_qualification_certificate_iii_aged_care" = "Certificate III Aged Care"
"professional.profile.field.personal_care_worker_qualification_certificate_iii_disabilities" = "Certificate III in Disabilities"
"professional.profile.field.personal_care_worker_qualification_certificate_iii_home_community_care" = "Certificate III in Home and Community Care"
"professional.profile.field.personal_care_worker_qualification_certificate_iii_individual_support" = "Certificate III Individual Support"
"professional.profile.field.personal_care_worker_qualification_certificate_iv_ageing_support" = "Certificate IV in Ageing Support"
"professional.profile.field.personal_care_worker_qualification_certificate_iv_disability" = "Certificate IV in Disability"
"professional.profile.field.personal_care_worker_qualification_certificate_iv_home_community_care" = "Certificate IV in Home and Community Care"
"professional.profile.field.personal_care_worker_qualification_diploma_nursing" = "Diploma of Nursing"
"professional.profile.field.photo" = "Photo"
"professional.profile.field.preferred_grade" = "Preferred Grade"
"professional.profile.field.preferred_locality" = "Preferred Location"
"professional.profile.field.preferred_specialities" = "Areas of Experience"
"professional.profile.field.preferred_speciality_other_name" = "Preferred Speciality Other Name"
"professional.profile.field.preferred_state" = "Preferred Location"
"professional.profile.field.profession" = "Your Profession"
"professional.profile.field.qualification" = "Qualification"
"professional.profile.field.qualification_end_date" = "Qualification End Date"
"professional.profile.field.qualification_name" = "Name of Qualification"
"professional.profile.field.references" = "References"
"professional.profile.field.statement_from_financial_institution" = "Statement From Financial Institution"
"professional.profile.field.tertiary_student_id_card" = "Tertiary Student ID Card"
"professional.profile.field.utility_bill_or_rate_notice" = "Utility Bill Or Rate Notice"
"professional.profile.field.visa" = "Visa"
"professional.profile.field.working_with_children_or_vulnerable_people" = "Working With Children / Vulnerable People Check"
"professional_file.name.abn" = "ABN"
"professional_file.name.additional_certification" = "Additional Certification"
"professional_file.name.ahpra_certificate" = "AHPRA Registration"
"professional_file.name.australian_birth_certificate" = "Australian birth certificate"
"professional_file.name.australian_citizenship_certificate" = "Australian citizenship certificate"
"professional_file.name.australian_passport" = "Australian passport (current or expired less than 2 years ago)"
"professional_file.name.australian_public_service_employee_id_card" = "Australian Public Service employee ID card with photo"
"professional_file.name.centrelink_or_pension_card" = "Centrelink or pension card"
"professional_file.name.commonwealth_statutory_declaration" = "Overseas Residency / Citizenship"
"professional_file.name.credit_debit_atm_card" = "Credit/Debit/ATM Card (maximum of one card from any one financial institution)"
"professional_file.name.current_australia_driver_licence" = "Current Australia driver licence"
"professional_file.name.current_immunisation_records" = "Current Immunisation Records"
"professional_file.name.curriculum_vitae" = "Experience CV"
"professional_file.name.disclosure" = "Disclosures"
"professional_file.name.fellowship_certificate" = "Areas of Experience Fellowship Certificate"
"professional_file.name.foreign_passport" = "Foreign passport"
"professional_file.name.indemnity_insurance_certificate" = "Indemnity Insurance Certificate"
"professional_file.name.medicare_card" = "Medicare card"
"professional_file.name.national_criminal_check" = "National Criminal Check"
"professional_file.name.other_australian_government_issue_id_card" = "Other Australian government issue ID card with photo"
"professional_file.name.personal_accident_illness_insurance_certificate" = "Personal Accident & Illness Insurance Certificate"
"professional_file.name.personal_care_worker_qualification_bachelor_nursing" = "Bachelor of Nursing"
"professional_file.name.personal_care_worker_qualification_cert_iii_disabilities" = "Certificate III in Disabilities"
"professional_file.name.personal_care_worker_qualification_cert_iii_individual_support" = "Certificate III Individual Support"
"professional_file.name.personal_care_worker_qualification_cert_iv_ageing_support" = "Certificate IV in Ageing Support"
"professional_file.name.personal_care_worker_qualification_cert_iv_disability" = "Certificate IV in Disability"
"professional_file.name.personal_care_worker_qualification_certificate_iii_aged_care" = "Certificate III Aged Care"
"professional_file.name.personal_care_worker_qualification_certificate_iii_home_community_care" = "Certificate III in Home and Community Care"
"professional_file.name.personal_care_worker_qualification_certificate_iv_home_community_care" = "Certificate IV in Home and Community Care"
"professional_file.name.personal_care_worker_qualification_diploma_nursing" = "Diploma of Nursing"
"professional_file.name.photo" = "Profile Photo"
"professional_file.name.qualification_certificate" = "Graduating Institution"
"professional_file.name.registrar_accredited_enrolment" = "Areas of Experience Registrar (Accredited) Enrolment"
"professional_file.name.signed_agreement" = "Signed Agreement"
"professional_file.name.specialist_qualification" = "Areas of Experience Specialist Qualification"
"professional_file.name.statement_from_financial_institution" = "Statement from a financial institution where you have held the account for at least one year"
"professional_file.name.tertiary_student_id_card" = "Tertiary student ID card with photo"
"professional_file.name.utility_bill_or_rate_notice" = "Utility bill or rate notice, for example water rates, council rates, electricity or gas; must be less than 12 months old"
"professional_file.name.visa" = "Visa"
"professional_file.name.working_with_children_or_vulnerable_people" = "Working With Children / Vulnerable People Check"
"reference_form.email.button1.text" = "Complete reference"
"reference_form.email.greeting" = "Hi"
"reference_form.email.instructions" = "To complete your reference for {{.ProfessionalName}}"
"reference_form.email.intro" = "{{.ProfessionalName}} has registered as {{.AOrAn}} {{.ProfessionName}} on the Medic Crew platform and nominated you as a referee.\n"
"reference_form.email.intro1" = "{{.ProfessionalName}} is seeking to provide the following services:\n"
"reference_form.email.intro2" = "Medic Crew helps connect healthcare providers with facilities seeking medical services, ensuring that practitioners with the right skills and interests are matched with facilities' needs. For More, visit www.mediccrew.com.au\n"
"reference_form.email.intro3" = "Thank you for taking the time to complete this request. Your timely response is appreciated as it will help us speed up the process for {{.ProfessionalName}}. You can do this using your mobile device and it should take no longer than a few minutes.\n"
"reference_form.email.signature" = "Thank you"
"reference_form.email.subject" = "Kindly complete a reference for {{.ProfessionalName}}"
"reference_form.email_verify.greeting" = "Hi"
"reference_form.email_verify.intro" = "You have been invited to provide a reference for {{.ProfessionalName}} on Medic Crew."
"reference_form.email_verify.intro1" = "To confirm your identity, please use the verification code below:"
"reference_form.email_verify.intro2" = "Please enter this code on the verification page to proceed."
"reference_form.email_verify.signature" = "Thank you"
"reference_form.email_verify.subject" = "Referee Identity Verification"
"register.email.greeting" = "Hi"
"register.email.instructions" = "Below is your registration activation code. Please enter it within 10 minutes."
"register.email.intros" = "Thank you for registering as a Medic Crew user."
"register.email.signature" = "Thank you"
"register.email.subject" = "Registration Verification Code"
"user.status.disable" = "This account is currently unable to sign in."
"user.type.not.please_use_username_and_id" = "This email address has already been registered using password, please enter your email and password to sign in."
"user.type.not.support_oauth" = "This account is not supported to sign in with OAuth."
"user_devices.apply.email.greeting" = "Hi"
"user_devices.apply.email.instructions" = "The following is the verification code, please fill in within 10 minutes."
"user_devices.apply.email.intros" = "A sign in attempt requires further verification because we did not recognize your device. To complete the sign in, enter the verification code on the unrecognized device."
"user_devices.apply.email.outros" = "If you did not attempt to sign in to your account, your password may be compromised. You may need to create a new, strong password for your account."
"user_devices.apply.email.signature" = "Thanks"
"user_devices.apply.email.subject" = "Please verify your device"
"user_devices.apply.failed" = "Incorrect account or password."
"user_devices.apply.no_email" = "Please setup your email first."
"user_devices.not_found" = "Device not found."
"user_devices.verify.code_does_not_exist" = "The verification has expired, please resend the verification code."
"user_devices.verify.incorrect_code" = "Incorrect verification code."
"user_devices.verify.too_much_try" = "Too many attempts, please try again later to resend the verification code."
"xexcel.import.date" = "Row %d: Column [%s] is not the correct date format."
"xexcel.import.db_exist" = "Row %d: Column %s dose not exist."
"xexcel.import.incorrect_title" = "Incorrect import heading."
"xexcel.import.oneof" = "Row %d: Column [%s] must be one of '%s'."
"xexcel.import.required" = "Row %d: Column [%s] can not be empty."
