package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xtool"
	"github.com/Norray/xrocket/xws"
	"github.com/gin-gonic/gin"
	"github.com/go-sql-driver/mysql"
	"github.com/gorilla/websocket"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

var WsMessageService = new(wsMessageService)

type wsMessageService struct{}

const (
	cacheKeyUserFacilityId = "cache:facility_id:user:%d" // 用戶FacilityId緩存
)

// region ---------------------------------------------------- WebSocket在線連接管理 ----------------------------------------------------

var wsOnlineConnLock sync.RWMutex
var WsOnlineConnMap = make(map[string]map[uint64]map[string]bool) // [targetType][targetId][connUuid]

// 添加WebSocket連接
func (s wsMessageService) AddWsOnlineConn(targetId uint64, targetType string, connUuid string) {
	wsOnlineConnLock.Lock()
	defer wsOnlineConnLock.Unlock()
	if _, ok := WsOnlineConnMap[targetType]; !ok {
		WsOnlineConnMap[targetType] = make(map[uint64]map[string]bool)
	}
	if _, ok := WsOnlineConnMap[targetType][targetId]; !ok {
		WsOnlineConnMap[targetType][targetId] = make(map[string]bool)
	}
	WsOnlineConnMap[targetType][targetId][connUuid] = true
}

// 移除WebSocket連接
func (s wsMessageService) RemoveWsOnlineConn(targetId uint64, targetType string, connUuid string) {
	wsOnlineConnLock.Lock()
	defer wsOnlineConnLock.Unlock()
	if _, ok := WsOnlineConnMap[targetType]; !ok {
		return
	}
	if _, ok := WsOnlineConnMap[targetType][targetId]; !ok {
		return
	}
	delete(WsOnlineConnMap[targetType][targetId], connUuid)
	if len(WsOnlineConnMap[targetType][targetId]) == 0 {
		delete(WsOnlineConnMap[targetType], targetId)
	}
	if len(WsOnlineConnMap[targetType]) == 0 {
		delete(WsOnlineConnMap, targetType)
	}
}

// 檢查WebSocket連接是否存在
func (s wsMessageService) CheckWsOnlineConn(targetId uint64, targetType string, connUuids ...string) bool {
	wsOnlineConnLock.RLock()
	defer wsOnlineConnLock.RUnlock()
	if _, ok := WsOnlineConnMap[targetType]; !ok {
		return false
	}
	if _, ok := WsOnlineConnMap[targetType][targetId]; !ok {
		return false
	}
	if len(connUuids) == 0 {
		return true
	}
	for _, connUuid := range connUuids {
		if _, ok := WsOnlineConnMap[targetType][targetId][connUuid]; !ok {
			return false
		}
	}
	return true
}

// 獲取WebSocket連接
func (s wsMessageService) GetWsOnlineConn(targetId uint64, targetType string) []string {
	wsOnlineConnLock.RLock()
	defer wsOnlineConnLock.RUnlock()
	var connUuids []string
	if _, ok := WsOnlineConnMap[targetType]; !ok {
		return connUuids
	}
	if _, ok := WsOnlineConnMap[targetType][targetId]; !ok {
		return connUuids
	}
	for connUuid := range WsOnlineConnMap[targetType][targetId] {
		connUuids = append(connUuids, connUuid)
	}
	return connUuids
}

// 上下線提醒
func (s wsMessageService) OnlineRemind(db *gorm.DB, onlineType string, senderId uint64, senderType string) error {
	var targetList []struct {
		TargetId   uint64
		TargetType string
	}
	if senderType == model.WsMessageSenderTypeProfessional {
		list, err := s.ProfessionalSessionList(db, WsSessionListReq{
			SearchKeyword:      "",
			BeforeTime:         "",
			Limit:              0,
			FacilityId:         0,
			ProfessionalUserId: senderId,
			ReqUserId:          senderId,
			ReqUserType:        senderType,
		})
		if err != nil {
			return err
		}
		for _, session := range list {
			if session.Online != "Y" {
				continue
			}
			targetList = append(targetList, struct {
				TargetId   uint64
				TargetType string
			}{
				TargetId:   session.FacilityId,
				TargetType: model.WsMessageSenderTypeFacility,
			})
		}
	} else if senderType == model.WsMessageSenderTypeFacility {
		list, err := s.FacilitySessionList(db, WsSessionListReq{
			SearchKeyword:      "",
			BeforeTime:         "",
			Limit:              0,
			FacilityId:         senderId,
			ProfessionalUserId: 0,
			ReqUserId:          senderId,
			ReqUserType:        senderType,
		})
		if err != nil {
			return err
		}
		for _, session := range list {
			if session.Online != "Y" {
				continue
			}
			targetList = append(targetList, struct {
				TargetId   uint64
				TargetType string
			}{
				TargetId:   session.ProfessionalUserId,
				TargetType: model.WsMessageSenderTypeProfessional,
			})
		}
	}

	if len(targetList) == 0 {
		return nil
	}

	for _, target := range targetList {
		_, sessionUuid, err := s.GetOrCreateSession(db, senderId, senderType, target.TargetId, target.TargetType, false)
		if err != nil {
			return err
		}
		s.SendMessageToUser(db, sessionUuid, "", "", target.TargetType, target.TargetId, model.WsMessage{
			MessageType: onlineType,
			SenderId:    senderId,
			SenderType:  senderType,
		})
	}

	return nil
}

// endregion ---------------------------------------------------- WebSocket在線連接管理 ----------------------------------------------------

// region ---------------------------------------------------- 消息發送限制（允許發送的消息類型和接收者類型） ----------------------------------------------------

var WsMessageSenderLimit = map[string]map[string]map[string]bool{
	model.WsMessageSenderTypeProfessional: {
		"MessageType": {
			model.WsMessageTypeText:        true, // 文字
			model.WsMessageTypeJobAccept:   true, // 接收工作邀請
			model.WsMessageTypeJobDecline:  true, // 拒絕工作邀請
			model.WsMessageTypeRead:        true, // 工作已讀
			model.WsMessageTypeUnreadCount: true, // 未讀消息數
		},
		"ReceiverType": {
			model.WsMessageReceiverTypeFacility: true, // 機構
			model.WsMessageReceiverTypeSystem:   true, // 機構
		},
	},
	model.WsMessageSenderTypeFacility: {
		"MessageType": {
			model.WsMessageTypeText:                 true, // 文字
			model.WsMessageTypeJobInvitation:        true, // 發出工作邀請
			model.WsMessageTypeJobInvitationRevoked: true, // 撤回工作邀請
			model.WsMessageTypeRead:                 true, // 工作已讀
			model.WsMessageTypeUnreadCount:          true, // 未讀消息數
		},
		"ReceiverType": {
			model.WsMessageReceiverTypeProfessional: true, // 專業人士
			model.WsMessageReceiverTypeSystem:       true, // 機構
		},
	},
	// 參數必填校驗
	"RequiredParams": {
		model.WsMessageTypeJobInvitation: {
			"JobId":            true,
			"JobApplicationId": true,
		},
		model.WsMessageTypeJobAccept: {
			"JobId":            true,
			"JobApplicationId": true,
		},
	},
}

// endregion ---------------------------------------------------- 消息發送限制（允許發送的消息類型和接收者類型） ----------------------------------------------------

type WsMessageReq struct {
	ClientUuid         string `json:"clientUuid"`                                                                                                      // 客戶端UUID
	MessageType        string `json:"messageType" binding:"required,oneof=TEXT JOB_INVITATION JOB_INVITATION_REVOKED JOB_ACCEPT JOB_DECLINE READ ACK"` // 消息類型 TEXT: 文字 JOB_INVITATION: 工作邀請 JOB_INVITATION_REVOKED: 工作邀請撤回 JOB_ACCEPT: 工作確認 JOB_DECLINE: 工作拒絕 READ: 消息已讀 ACK: 確認消息
	Content            string `json:"content" binding:"required_if=MessageType TEXT"`                                                                  // 消息內容
	ReceiverId         uint64 `json:"receiverId" binding:"required"`                                                                                   // 接收者ID (專業人士UserId或機構FacilityId)
	ReceiverType       string `json:"receiverType" binding:"required,oneof=PROFESSIONAL FACILITY"`                                                     // 接收者類型
	JobId              uint64 `json:"jobId"`                                                                                                           // 相關工作ID
	JobApplicationId   uint64 `json:"jobApplicationId"`                                                                                                // 相關工作申請ID
	RelatedMessageUuid string `json:"relatedMessageUuid"`                                                                                              // 關聯的消息UUID(如果是回覆消息，則指向原消息UUID)
}

// region ---------------------------------------------------- 檢查會話是否存在 - 專業人士 ----------------------------------------------------

func (s wsMessageService) CheckProfessionalSessionExist(db *gorm.DB, professionalUserId uint64, sessionUuid string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.ws_message.id.session_not_found",
		Other: "Session not found.",
	}
	var err error
	var session model.WsSession
	if err = db.Where("professional_user_id = ? AND session_uuid = ?", professionalUserId, sessionUuid).First(&session).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 檢查會話是否存在 - 專業人士 ----------------------------------------------------

// region ---------------------------------------------------- 檢查會話是否存在 - 機構 ----------------------------------------------------

func (s wsMessageService) CheckFacilitySessionExist(db *gorm.DB, facilityId uint64, sessionUuid string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.ws_message.id.session_not_found",
		Other: "Session not found.",
	}
	var err error
	var session model.WsSession
	if err = db.Where("facility_id = ? AND session_uuid = ?", facilityId, sessionUuid).First(&session).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 檢查會話是否存在 - 機構 ----------------------------------------------------

// region ---------------------------------------------------- 檢查消息是否已經處理 ----------------------------------------------------

func (s wsMessageService) CheckMessageProcessed(db *gorm.DB, messageUuid string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.ws_message.id.message_not_found",
		Other: "Message not found.",
	}
	var err error
	var message model.WsMessage
	if err = db.Where("message_uuid = ?", messageUuid).First(&message).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	if message.Processed == "Y" {
		return false, i18n.Message{
			ID:    "checker.ws_message.id.message_already_processed",
			Other: "Message already processed.",
		}, nil
	}
	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 檢查消息是否已經處理 ----------------------------------------------------

// 傳輸的WebSocket消息
type WsMessageTransport struct {
	MessageType        string      `json:"messageType,omitempty"`        // 消息類型 TEXT=文字 JOB_INVITATION=工作邀請 JOB_INVITATION_REVOKED=工作邀請撤回 JOB_ACCEPT=工作確認 JOB_DECLINE=工作拒絕 READ=消息已讀 ACK=確認消息
	SessionUuid        string      `json:"sessionUuid,omitempty"`        // 聊天會話UUID
	MessageUuid        string      `json:"messageUuid,omitempty"`        // 消息UUID
	RelatedMessageUuid string      `json:"relatedMessageUuid,omitempty"` // 關聯的消息UUID
	JobId              uint64      `json:"jobId,omitempty"`              // 工作ID (工作相關)
	JobApplicationId   uint64      `json:"jobApplicationId,omitempty"`   // 工作申請ID (工作相關)
	ClientUuid         string      `json:"clientUuid,omitempty"`         // 客戶端UUID
	Content            string      `json:"content,omitempty"`            // 消息內容
	SenderId           uint64      `json:"senderId,omitempty"`           // 發送者ID
	SenderType         string      `json:"senderType,omitempty"`         // 發送者類型 PROFESSIONAL: 專業人士, FACILITY: 機構
	Status             string      `json:"status,omitempty"`             // 消息狀態 UNREAD: 未讀, READ: 已讀 DELETED: 已刪除
	Processed          string      `json:"processed,omitempty"`          // 消息處理狀態 N: 未處理, Y: 已處理
	ProcessResult      string      `json:"processResult,omitempty"`      // 用戶處理結果
	Extra              interface{} `json:"extra,omitempty"`              // 附加信息
	CreateTime         time.Time   `json:"createTime,omitempty"`         // 消息創建時間
	ErrorMsg           string      `json:"errorMsg,omitempty"`           // 錯誤信息
}

// WebSocket消息任務
type WsMessageTask struct {
	UserId  uint64          `json:"userId"`  // 接收者ID
	Message json.RawMessage `json:"message"` // 消息內容（JSON格式）
}

type HandleWebSocketConnectionReq struct {
	FacilityId  uint64 `json:"facilityId"`  // 機構ID(機構用戶)
	ReqUserId   uint64 `json:"reqUserId"`   // 請求用戶ID
	ReqUserType string `json:"reqUserType"` // 請求用戶類型
}

// region ---------------------------------------------------- Get User Facility Id ----------------------------------------------------

func (s wsMessageService) GetUserFacilityId(nc xapp.NGinCtx, db *gorm.DB) (uint64, error) {
	cacheKey := fmt.Sprintf(cacheKeyUserFacilityId, nc.GetJWTUserId())
	var facilityId uint64
	exist, err := xredis.GetStruct(nc.C, cacheKey, &facilityId)
	if err != nil {
		return 0, err
	}
	if exist {
		return facilityId, nil
	}
	var facilityUser model.FacilityUser
	if err = db.Where("user_id = ?", nc.GetJWTUserId()).First(&facilityUser).Error; err != nil {
		return 0, err
	}
	if err = xredis.SetStruct(nc.C, cacheKey, facilityUser.FacilityId, time.Minute); err != nil {
		return 0, err
	}
	return facilityUser.FacilityId, nil
}

// endregion ---------------------------------------------------- Get User Facility Id ----------------------------------------------------

// region ---------------------------------------------------- Handle WebSocket Connection ----------------------------------------------------

func (s wsMessageService) GetWsSessionConnUuid(senderId uint64, senderType string) string {
	var idStr string
	if senderType == model.WsMessageSenderTypeProfessional {
		idStr = fmt.Sprintf("user_%d", senderId)
	} else {
		idStr = fmt.Sprintf("facility_%d", senderId)
	}
	namespace := uuid.NewV5(uuid.NamespaceDNS, xconfig.AppConf.AppName)
	return uuid.NewV5(namespace, idStr).String()
}

// 處理WebSocket連接
func (s wsMessageService) HandleWebSocketConnection(c *gin.Context, req HandleWebSocketConnectionReq) error {
	nc := xapp.NGinCtx{C: c}
	db := xgorm.DB.WithContext(c)
	traceId := nc.GetTraceId()
	logger := log.WithField("traceId", traceId).WithField("userId", req.ReqUserId).WithField("userType", req.ReqUserType)
	userId := nc.GetJWTUserId()

	lang := nc.GetLanguage()

	logger.Infof("websocket connection")

	var u = websocket.Upgrader{
		ReadBufferSize:  1024 * 30,
		WriteBufferSize: 1024 * 30,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	header := http.Header{}
	header.Add("Sec-WebSocket-Protocol", c.Request.Header.Get("Sec-WebSocket-Protocol"))
	conn, err := u.Upgrade(c.Writer, c.Request, header)
	if err != nil {
		logger.Errorf("websocket upgrade failed: %v", err)
		return err
	}
	defer conn.Close()

	// 創建 xws 連接
	var senderId uint64 // 專業人士UserId 或 機構FacilityId
	if req.ReqUserType == model.WsMessageSenderTypeProfessional {
		senderId = req.ReqUserId
	} else {
		senderId = req.FacilityId
	}

	sessionConnUuid := s.GetWsSessionConnUuid(senderId, req.ReqUserType)

	connUuid := uuid.NewV4().String()
	wsConn := &xws.Conn{
		C:           conn,
		UserId:      req.ReqUserId,
		ConnUuid:    connUuid,
		SessionUuid: sessionConnUuid,
	}

	// 添加連接到 xws
	if err = xws.AddConn(wsConn); err != nil {
		logger.Errorf("add websocket connection failed: %v", err)
		return err
	}
	s.AddWsOnlineConn(senderId, req.ReqUserType, connUuid)
	_ = s.OnlineRemind(db, model.WsMessageTypeOnline, senderId, req.ReqUserType)

	// 連接關閉時移除
	defer xws.RemoveConn(sessionConnUuid, connUuid)
	defer s.RemoveWsOnlineConn(senderId, req.ReqUserType, connUuid)
	defer s.OnlineRemind(db, model.WsMessageTypeOffline, senderId, req.ReqUserType)

	// 持續讀取消息
	for {
		var message []byte
		_, message, err = conn.ReadMessage()
		if err != nil {
			break
		}
		handleMessageReq := HandleMessageReq{
			SenderId:        senderId,
			SenderType:      req.ReqUserType,
			ReqUserId:       req.ReqUserId,
			FacilityId:      req.FacilityId,
			SessionConnUuid: sessionConnUuid,
			ConnUuid:        connUuid,
			UserId:          userId,
		}

		var msgReq WsMessageReq
		if err = json.Unmarshal(message, &msgReq); err != nil {
			logger.Errorf("[ws] unmarshal message failed: %v", err)
			s.HandleError(sessionConnUuid, msgReq, handleMessageReq, xi18n.LocalizeWithLang(lang, &i18n.Message{
				ID:    "checker.ws_message.id.unmarshal_message_failed",
				Other: "Some thing went wrong, please try again later.",
			}))
			continue
		}

		ok, msg, err := s.CheckWsMessage(nc, db, msgReq, handleMessageReq)
		if err != nil {
			logger.Errorf("[ws] check message failed: %v", err)
			s.HandleError(sessionConnUuid, msgReq, handleMessageReq, xi18n.LocalizeWithLang(lang, &i18n.Message{
				ID:    "checker.ws_message.id.check_message_failed",
				Other: "Some thing went wrong, please try again later.",
			}))
			continue
		}

		if !ok {
			logger.Errorf("[ws] check message failed: %v", msg)
			s.HandleError(sessionConnUuid, msgReq, handleMessageReq, msg)
			continue
		}

		// 處理接收到的消息
		err = db.Transaction(func(tx *gorm.DB) error {
			return s.HandleMessage(tx, msgReq, handleMessageReq)
		})

		if err != nil {
			logger.Errorf("[ws] handle message failed: %v", err)
			s.HandleError(sessionConnUuid, msgReq, handleMessageReq, xi18n.LocalizeWithLang(lang, &i18n.Message{
				ID:    "checker.ws_message.id.handle_message_failed",
				Other: "Some thing went wrong, please try again later.",
			}))
		}
	}
	return nil
}

// 檢查請求數據是否正確
func (s wsMessageService) CheckWsMessage(nc xapp.NGinCtx, db *gorm.DB, wsMessage WsMessageReq, handleMessageReq HandleMessageReq) (bool, string, error) {
	checker := xapp.NewCK(nc.C, true)
	lang := nc.GetLanguage()

	var msg i18n.Message
	var err error
	// 檢查MessageType是否在限制列表中
	if !WsMessageSenderLimit[handleMessageReq.SenderType]["MessageType"][wsMessage.MessageType] {
		msg = i18n.Message{
			ID:    "checker.ws_message.id.message_type_not_allowed",
			Other: "Message type not allowed.",
		}
		return false, xi18n.LocalizeWithLang(lang, &msg), nil
	}
	// 檢查ReceiverType是否在限制列表中
	if !WsMessageSenderLimit[handleMessageReq.SenderType]["ReceiverType"][wsMessage.ReceiverType] {
		if handleMessageReq.SenderType == model.WsMessageSenderTypeProfessional {
			msg = i18n.Message{
				ID:    "checker.ws_message.id.professional_cannot_send_to_facility",
				Other: "cannot send messages to facility.",
			}
		} else if handleMessageReq.SenderType == model.WsMessageSenderTypeFacility {
			msg = i18n.Message{
				ID:    "checker.ws_message.id.facility_cannot_send_to_professional",
				Other: "cannot send messages to professional.",
			}
		}
		return false, xi18n.LocalizeWithLang(lang, &msg), nil
	}
	// 檢查必填參數是否缺失
	if _, ok := WsMessageSenderLimit["RequiredParams"][wsMessage.MessageType]; ok {
		missing := false
		for param, _ := range WsMessageSenderLimit["RequiredParams"][wsMessage.MessageType] {
			if wsMessage.JobId == 0 && param == "JobId" {
				missing = true
				break
			}
			if wsMessage.JobApplicationId == 0 && param == "JobApplicationId" {
				missing = true
				break
			}
		}
		if missing {
			msg = i18n.Message{
				ID:    "checker.ws_message.id.required_param_missing",
				Other: "Required parameter missing.",
			}
			return false, xi18n.LocalizeWithLang(lang, &msg), nil
		}
	}
	// 檢查專業人士和機構是否可以發送消息
	if wsMessage.ReceiverType != model.WsMessageReceiverTypeSystem {
		if handleMessageReq.SenderType == model.WsMessageSenderTypeProfessional {
			checker.Run(func() (bool, i18n.Message, error) {
				return JobApplicationService.CheckFacilityProfessionalApplicationByUserId(db, wsMessage.ReceiverId, handleMessageReq.SenderId)
			})
		} else if handleMessageReq.SenderType == model.WsMessageSenderTypeFacility {
			checker.Run(func() (bool, i18n.Message, error) {
				return JobApplicationService.CheckFacilityProfessionalApplicationByUserId(db, handleMessageReq.SenderId, wsMessage.ReceiverId)
			})
		}
	}
	needCheckProcessed := false
	switch wsMessage.MessageType {
	case model.WsMessageTypeJobInvitationRevoked,
		model.WsMessageTypeJobAccept,
		model.WsMessageTypeJobDecline:
		needCheckProcessed = true
	}
	if needCheckProcessed {
		checker.Run(func() (bool, i18n.Message, error) {
			return s.CheckMessageProcessed(db, wsMessage.RelatedMessageUuid)
		})
	}
	switch wsMessage.MessageType {
	case model.WsMessageTypeJobAccept:
		checker.Run(func() (bool, i18n.Message, error) {
			return JobService.CheckCanAcceptInvite(db, wsMessage.ReceiverId, wsMessage.JobId, wsMessage.JobApplicationId)
		})
	case model.WsMessageTypeJobInvitation:
		checker.Run(func() (bool, i18n.Message, error) {
			return JobService.CheckCanInvite(db, handleMessageReq.FacilityId, wsMessage.JobId, wsMessage.JobApplicationId)
		})
	case model.WsMessageTypeJobInvitationRevoked:
		checker.Run(func() (bool, i18n.Message, error) {
			return JobService.CheckCanRevokeInvite(db, handleMessageReq.FacilityId, wsMessage.JobId, wsMessage.JobApplicationId)
		})
	}
	checkMsg, err := checker.Result()
	if err != nil {
		return false, "", err
	}
	if len(checkMsg) > 0 {
		return false, checkMsg[0], nil
	}
	return true, "", nil
}

// endregion ---------------------------------------------------- Handle WebSocket Connection ----------------------------------------------------

// region ---------------------------------------------------- Message ----------------------------------------------------

type HandleMessageReq struct {
	SenderId        uint64 // 發送者ID (專業人士UserId或機構FacilityId)
	SenderType      string // 發送者類型 (PROFESSIONAL: 專業人士, FACILITY: 機構, SYSTEM: 系統)
	ReqUserId       uint64 // 請求的用戶Id
	FacilityId      uint64 // 機構才有
	SessionConnUuid string // 當前會話連接UUID
	ConnUuid        string // 當前連接UUID
	UserId          uint64 // 當前用戶Id
}

// 處理接收到的消息
func (s wsMessageService) HandleMessage(tx *gorm.DB, wsMessageReq WsMessageReq, handleMessageReq HandleMessageReq) error {
	var err error
	// 獲取或創建聊天會話
	sessionId, sessionUuid, err := s.GetOrCreateSession(tx, handleMessageReq.SenderId, handleMessageReq.SenderType, wsMessageReq.ReceiverId, wsMessageReq.ReceiverType, false)
	if err != nil {
		return err
	}
	var changed bool
	var processResult string
	// 處理業務消息
	switch wsMessageReq.MessageType {
	case model.WsMessageTypeJobInvitation: // 機構發出工作邀請
		err = s.HandleJobInvitation(tx, handleMessageReq.FacilityId, wsMessageReq.JobId, wsMessageReq.JobApplicationId, handleMessageReq.ReqUserId)
	case model.WsMessageTypeJobInvitationRevoked: // 機構撤回工作邀請
		err = s.HandleJobInvitationRevoked(tx, handleMessageReq.FacilityId, wsMessageReq.JobId, wsMessageReq.JobApplicationId)
		processResult = model.WsMessageProcessResultFacilityRevoke
	case model.WsMessageTypeJobAccept: // 專業人士確認接受工作邀請
		err = s.HandleJobConfirm(tx, handleMessageReq.FacilityId, wsMessageReq.JobId, wsMessageReq.JobApplicationId, handleMessageReq.ReqUserId)
		processResult = model.WsMessageProcessResultProfessionalAccept
	case model.WsMessageTypeJobDecline: // 專業人士拒絕工作邀請
		err = s.HandleJobDecline(tx, wsMessageReq.JobId, wsMessageReq.JobApplicationId, handleMessageReq.ReqUserId)
		processResult = model.WsMessageProcessResultProfessionalDecline
	case model.WsMessageTypeRead: // 消息已讀
		changed, err = s.HandleMessageRead(tx, sessionId, handleMessageReq)
	}
	if err != nil {
		return err
	}
	// 如果存在相關消息，則更新處理結果
	if wsMessageReq.RelatedMessageUuid != "" && processResult != "" {
		updateMap := map[string]interface{}{
			"process_result": processResult,
			"processed":      model.WsMessageProcessedY,
		}
		if err = tx.Model(&model.WsMessage{}).
			Where("message_uuid = ?", wsMessageReq.RelatedMessageUuid).
			Updates(updateMap).Error; err != nil {
			return err
		}
	}

	needCreateMessage := true
	switch wsMessageReq.MessageType {
	case model.WsMessageTypeJobInvitationRevoked:
		// 不需要生成消息
		needCreateMessage = false
		// 通知對方此工作邀請已取消
		s.SendMessageToUser(tx, sessionUuid, "", "", wsMessageReq.ReceiverType, wsMessageReq.ReceiverId, model.WsMessage{
			MessageType:        model.WsMessageTypeJobInvitationRevoked,
			SenderId:           handleMessageReq.SenderId,
			SenderType:         handleMessageReq.SenderType,
			ReceiverId:         wsMessageReq.ReceiverId,
			ReceiverType:       wsMessageReq.ReceiverType,
			RelatedMessageUuid: wsMessageReq.RelatedMessageUuid,
			ProcessResult:      processResult,
		})
	case model.WsMessageTypeRead:
		if changed {
			// 通知對方消息已讀
			s.SendMessageToUser(tx, sessionUuid, "", "", wsMessageReq.ReceiverType, wsMessageReq.ReceiverId, model.WsMessage{
				MessageType:  model.WsMessageTypeRead,
				SenderId:     handleMessageReq.SenderId,
				SenderType:   handleMessageReq.SenderType,
				ReceiverId:   wsMessageReq.ReceiverId,
				ReceiverType: wsMessageReq.ReceiverType,
			})
		}
		needCreateMessage = false
	case model.WsMessageTypeUnreadCount:
		unreadResp, err := s.UnreadCount(tx, UnreadCountReq{
			UserType:   handleMessageReq.SenderType,
			UserId:     handleMessageReq.SenderId,
			FacilityId: handleMessageReq.FacilityId,
		})
		if err != nil {
			return err
		}

		// 返回自己的未讀消息數
		s.SendMessageToUser(tx, sessionUuid, handleMessageReq.ConnUuid, "", handleMessageReq.SenderType, handleMessageReq.SenderId, model.WsMessage{
			MessageType: model.WsMessageTypeUnreadCount,
			Content:     strconv.Itoa(unreadResp.UnreadCount),
		})
		return nil
	case model.WsMessageTypeText,
		model.WsMessageTypeJobInvitation,
		model.WsMessageTypeJobAccept,
		model.WsMessageTypeJobDecline:
		break
	}

	var message model.WsMessage
	nowTime := time.Now().UTC().Truncate(time.Second)
	messageUuid := wsMessageReq.ClientUuid
	if messageUuid == "" {
		messageUuid = uuid.NewV4().String()
	}

	// 創建消息
	message = model.WsMessage{
		MessageUuid:        messageUuid,
		SessionId:          sessionId,
		SenderId:           handleMessageReq.SenderId,
		SenderType:         handleMessageReq.SenderType,
		ReceiverId:         wsMessageReq.ReceiverId,
		ReceiverType:       wsMessageReq.ReceiverType,
		MessageType:        wsMessageReq.MessageType,
		Content:            wsMessageReq.Content,
		Processed:          model.WsMessageProcessedN,
		Status:             model.WsMessageStatusUnread,
		CreateTime:         nowTime,
		RelatedMessageId:   0, // TODO: 不需要這個字段
		RelatedMessageUuid: wsMessageReq.RelatedMessageUuid,
	}

	// 工作相關信息
	switch wsMessageReq.MessageType {
	case model.WsMessageTypeJobInvitation,
		model.WsMessageTypeJobInvitationRevoked,
		model.WsMessageTypeJobAccept,
		model.WsMessageTypeJobDecline:
		message.JobId = wsMessageReq.JobId
		message.JobApplicationId = wsMessageReq.JobApplicationId
	}

	if needCreateMessage {
		// 如果出現重複的MessageUuid錯誤，則生成新的MessageUuid
		if err = tx.Create(&message).Error; err != nil {
			var mysqlErr *mysql.MySQLError
			if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
				// 重複鍵錯誤，重新生成 UUID 再插入一次
				message.MessageUuid = uuid.NewV4().String()
				if err = tx.Create(&message).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}

		// 更新未讀消息數
		switch wsMessageReq.MessageType {
		case model.WsMessageTypeText,
			model.WsMessageTypeJobInvitation,
			model.WsMessageTypeJobInvitationRevoked,
			model.WsMessageTypeJobAccept,
			model.WsMessageTypeJobDecline:
			// 更新聊天會話最後一條消息
			updateData := map[string]interface{}{
				"last_message_id":      message.Id,
				"last_message_time":    nowTime,
				"last_message_type":    message.MessageType,
				"last_message_content": message.Content,
			}
			if wsMessageReq.ReceiverType == model.WsMessageSenderTypeProfessional {
				updateData["professional_unread_count"] = gorm.Expr("professional_unread_count + 1")
			} else {
				updateData["facility_unread_count"] = gorm.Expr("facility_unread_count + 1")
			}
			if err = tx.Model(&model.WsSession{}).Where("id = ?", sessionId).Updates(updateData).Error; err != nil {
				return err
			}
			// 更新會話最後一條消息
			if err = tx.Model(&model.WsSessionView{}).
				Where("session_id = ?", sessionId).
				Where("facility_id = ?", handleMessageReq.FacilityId).
				Update("last_interact_time", nowTime).Error; err != nil {
				return err
			}
		}
		// 向接收者發送消息
		s.SendMessageToUser(tx, sessionUuid, "", "", wsMessageReq.ReceiverType, wsMessageReq.ReceiverId, message)
		s.SendMessageToUser(tx, sessionUuid, "", handleMessageReq.ConnUuid, handleMessageReq.SenderType, handleMessageReq.SenderId, message)
	} else {
		// 如果不需要記錄消息，則清空相關字段
		message.MessageUuid = wsMessageReq.RelatedMessageUuid
		message.RelatedMessageId = 0
	}

	// 回復ACK（不記錄到消息表）
	s.SendMessageToUser(tx, sessionUuid, handleMessageReq.ConnUuid, "", handleMessageReq.SenderType, handleMessageReq.SenderId, model.WsMessage{
		MessageUuid:        message.MessageUuid,
		SessionId:          sessionId,
		SenderId:           0,
		SenderType:         model.WsMessageSenderTypeSystem,
		ReceiverId:         handleMessageReq.SenderId,
		ReceiverType:       handleMessageReq.SenderType,
		RelatedMessageId:   message.Id,
		RelatedMessageUuid: message.RelatedMessageUuid,
		JobId:              0,
		JobApplicationId:   0,
		MessageType:        model.WsMessageTypeAck,
		Content:            message.Content,
		Status:             model.WsMessageStatusRead,
		Processed:          model.WsMessageProcessedY,
		ProcessResult:      processResult,
		ReadTime:           &nowTime,
		CreateTime:         nowTime,
	})

	return nil
}

func (s wsMessageService) HandleError(sessionConnUuid string, req WsMessageReq, handleMessageReq HandleMessageReq, errMsg string) {
	var err error
	logger := log.WithFields(log.Fields{
		"sessionConnUuid": sessionConnUuid,
		"connUuid":        handleMessageReq.ConnUuid,
		"senderId":        handleMessageReq.SenderId,
		"senderType":      handleMessageReq.SenderType,
	})

	nowTime := time.Now().UTC().Truncate(time.Second)
	transport := WsMessageTransport{
		MessageType: model.WsMessageTypeError,
		ClientUuid:  req.ClientUuid,
		CreateTime:  nowTime,
		ErrorMsg:    errMsg,
	}
	var messageData []byte
	messageData, err = json.Marshal(transport)
	if err != nil {
		logger.Errorf("[ws] marshal message failed: %v", err)
		return
	}

	// 使用 xws 發送消息
	err = xws.AddToSendMsgQueue(xws.WsMsgTask{
		SessionUuid: sessionConnUuid,
		ConnUuid:    handleMessageReq.ConnUuid,
		MsgType:     model.WsMessageTypeError,
		Data:        messageData,
	})
	if err != nil {
		logger.Errorf("[ws] send error message to client failed: %v", err)
	}
}

// 處理工作邀請
func (s wsMessageService) HandleJobInvitation(tx *gorm.DB, facilityId, jobId, jobApplicationId, userId uint64) error {
	err := JobService.InviteProfessional(tx, JobInviteProfessionalReq{
		FacilityId:       facilityId,
		JobId:            jobId,
		JobApplicationId: jobApplicationId,
		InviterUserId:    userId,
	})
	return err
}

// 處理工作邀請撤回
func (s wsMessageService) HandleJobInvitationRevoked(tx *gorm.DB, facilityId, jobId, jobApplicationId uint64) error {
	return JobService.WithdrawInvite(tx, JobWithdrawInviteReq{
		FacilityId:       facilityId,
		JobId:            jobId,
		JobApplicationId: jobApplicationId,
	})
}

// 處理工作確認
func (s wsMessageService) HandleJobConfirm(tx *gorm.DB, facilityId, jobId, jobApplicationId, userId uint64) error {
	return JobApplicationService.HandleAcceptInvite(tx, JobAcceptInviteReq{
		FacilityId:       facilityId,
		JobId:            jobId,
		JobApplicationId: jobApplicationId,
		ReqUserId:        userId,
	})
}

// 處理工作拒絕
func (s wsMessageService) HandleJobDecline(tx *gorm.DB, jobId, jobApplicationId uint64, userId uint64) error {
	return JobApplicationService.HandleDeclineInvite(tx, JobDeclineInviteReq{
		JobId:            jobId,
		JobApplicationId: jobApplicationId,
		ReqUserId:        userId,
	})
}

// 處理消息已讀
func (s wsMessageService) HandleMessageRead(tx *gorm.DB, sessionId uint64, handleMessageReq HandleMessageReq) (bool, error) {
	var err error
	changed := false
	builder := tx.Model(&model.WsMessage{}).
		Where("session_id = ? ", sessionId).
		Where("receiver_id = ?", handleMessageReq.SenderId).
		Where("status = ?", model.WsMessageStatusUnread)

	result := builder.Update("status", model.WsMessageStatusRead)
	if err = result.Error; err != nil {
		return false, err
	}
	if result.RowsAffected > 0 {
		changed = true
	}

	if handleMessageReq.SenderType == model.WsMessageSenderTypeProfessional {
		if err = tx.Model(&model.WsSession{}).
			Where("id = ?", sessionId).
			Update("professional_unread_count", 0).Error; err != nil {
			return false, err
		}
	} else if handleMessageReq.SenderType == model.WsMessageSenderTypeFacility {
		if err = tx.Model(&model.WsSession{}).
			Where("id = ?", sessionId).
			Update("facility_unread_count", 0).Error; err != nil {
			return false, err
		}
	}
	return changed, nil
}

// 獲取或創建聊天會話
func (s wsMessageService) GetOrCreateSession(db *gorm.DB, senderId uint64, senderType string, receiverId uint64, receiverType string, createIfNotFound bool) (uint64, string, error) {
	var facilityId, professionalUserId uint64
	if senderType == model.WsMessageSenderTypeProfessional {
		professionalUserId = senderId
		facilityId = receiverId
	} else {
		facilityId = senderId
		professionalUserId = receiverId
	}

	var session model.WsSession
	err := db.Where("facility_id = ? AND professional_user_id = ?", facilityId, professionalUserId).First(&session).Error
	if xgorm.IsSqlErr(err) {
		return 0, "", err
	}

	if xgorm.IsNotFoundErr(err) {
		// 創建新聊天會話
		now := time.Now()
		session = model.WsSession{
			SessionUuid:        uuid.NewV4().String(),
			FacilityId:         facilityId,
			ProfessionalUserId: professionalUserId,
			LastMessageTime:    now,
			CreateTime:         now,
		}

		if err = db.Create(&session).Error; err != nil {
			return 0, "", err
		}
		sessionViews := []model.WsSessionView{
			{
				SessionId:          session.Id,
				ProfessionalUserId: professionalUserId,
				LastInteractTime:   now,
			},
			{
				SessionId:        session.Id,
				FacilityId:       facilityId,
				LastInteractTime: now,
			},
		}
		if err = db.Create(&sessionViews).Error; err != nil {
			return 0, "", err
		}

		return session.Id, session.SessionUuid, nil
	}

	return session.Id, session.SessionUuid, nil
}

// 獲取聊天會話
func (s wsMessageService) GetSession(db *gorm.DB, facilityId uint64, professionalUserId uint64) (*model.WsSession, error) {
	var session model.WsSession
	err := db.Where("facility_id = ? AND professional_user_id = ?", facilityId, professionalUserId).First(&session).Error
	if err != nil && !xgorm.IsNotFoundErr(err) {
		return nil, err
	}

	return &session, nil
}

// 獲取或創建消息聊天會話
func (s wsMessageService) GetOrCreateSystemSession(db *gorm.DB, userId uint64, userType string) (string, error) {
	// 系統消息的聊天會話命名規則: SYSTEM_[USER_TYPE]_[USER_ID]
	var facilityId, professionalId uint64

	// 根據用戶類型設置相應的字段
	if userType == model.WsMessageSenderTypeProfessional {
		professionalId = userId
	} else {
		facilityId = userId
	}

	session, err := s.GetSession(db, facilityId, professionalId)
	if err != nil {
		return "", err
	}

	// 如果聊天會話不存在，創建一個新的
	if xgorm.IsNotFoundErr(err) {
		now := time.Now()
		// 創建系統聊天會話
		session = &model.WsSession{
			FacilityId:         facilityId,
			ProfessionalUserId: professionalId,
			LastMessageTime:    now,
			CreateTime:         now,
		}

		if err := db.Create(&session).Error; err != nil {
			return "", err
		}
	}

	return fmt.Sprintf("%d", session.Id), nil
}

// 向用戶發送消息
// sessionUuid 必須 哪兩個主體會話
// connUuid 可選 當前連接，如果有則只會發送到此連接
// ignoreConnUuid 可選 忽略的連接，如果有忽略的連接
// userType 必須 用戶類型
// targetId 必須 用戶ID
// message 必須 消息
func (s wsMessageService) SendMessageToUser(db *gorm.DB, sessionUuid string, connUuid string, ignoreConnUuid string, userType string, targetId uint64, message model.WsMessage) {
	// 將消息轉換為JSON
	transport, err := s.GenerateMessageContent(db, sessionUuid, message)
	if err != nil {
		log.Errorf("[ws] generate message content failed: %v", err)
		return
	}
	messageData, err := json.Marshal(transport)
	if err != nil {
		log.Errorf("[ws] marshal message content failed: %v", err)
		return
	}
	sessionConnUuid := s.GetWsSessionConnUuid(targetId, userType)
	var connUuids []string
	if connUuid != "" {
		connUuids = []string{connUuid}
	} else if ignoreConnUuid != "" {
		connUuids = s.GetWsOnlineConn(targetId, userType)
	} else {
		connUuids = []string{""}
	}
	for _, cUuid := range connUuids {
		if cUuid != "" && cUuid == ignoreConnUuid {
			continue
		}
		// 使用 xws 發送消息
		err = xws.AddToSendMsgQueue(xws.WsMsgTask{
			SessionUuid: sessionConnUuid,
			ConnUuid:    cUuid,
			MsgType:     message.MessageType,
			Data:        messageData,
		})
		if err != nil {
			log.Errorf("[ws] add message to send queue failed: %v", err)
		}
	}
}

// 生成消息內容
func (s wsMessageService) GenerateMessageContent(db *gorm.DB, sessionUuid string, message model.WsMessage) (WsMessageTransport, error) {
	var err error
	if message.CreateTime.IsZero() {
		message.CreateTime = time.Now().UTC().Truncate(time.Second)
	}

	transport := WsMessageTransport{
		SessionUuid:        sessionUuid,
		MessageUuid:        message.MessageUuid,
		RelatedMessageUuid: message.RelatedMessageUuid,
		MessageType:        message.MessageType,
		Content:            message.Content,
		SenderId:           message.SenderId,
		SenderType:         message.SenderType,
		Status:             message.Status,
		Processed:          message.Processed,
		ProcessResult:      message.ProcessResult,
		CreateTime:         message.CreateTime,
	}
	switch message.MessageType {
	case model.WsMessageTypeJobInvitation, // 工作邀請
		model.WsMessageTypeJobAccept: // 工作確認
		transport.JobId = message.JobId
		transport.JobApplicationId = message.JobApplicationId
		transport.Extra, err = JobApplicationService.GetJobApplicationDetail(db, message.JobApplicationId)
		if err != nil {
			return transport, err
		}
		// 更新工作申請的邀請時間
		jobApplicationUpdateMap := map[string]interface{}{
			"invite_time": message.CreateTime,
		}
		if err = db.Model(&model.JobApplication{}).
			Where("facility_id = ?", message.SenderId).
			Where("professional_id = ?", message.ReceiverId).
			Where("job_id = ?", message.JobId).
			Where("id = ?", message.JobApplicationId).
			Updates(jobApplicationUpdateMap).Error; err != nil {
			return transport, err
		}
	}
	return transport, nil
}

// endregion ---------------------------------------------------- Message ----------------------------------------------------

// region ---------------------------------------------------- Session List ----------------------------------------------------

// 聊天會話列表請求
type WsSessionListReq struct {
	SearchKeyword      string `form:"searchKeyword"` // 搜索關鍵字
	BeforeTime         string `form:"beforeTime"`    // 開始時間(UTC)
	Limit              int    `form:"limit"`         // 限制數量(最大50)
	FacilityId         uint64 `form:"-" json:"-"`    // 機構ID
	ProfessionalUserId uint64 `form:"-" json:"-"`    // 專業人士ID
	ReqUserId          uint64 `form:"-" json:"-"`    // 請求用戶ID
	ReqUserType        string `form:"-" json:"-"`    // 請求用戶類型
	CheckBlacklist     bool   `form:"-" json:"-"`    // 檢查黑名單
}

type WsSessionListResult struct {
	SessionUuid             string `json:"sessionUuid"`             // 聊天會話UUID
	FacilityId              uint64 `json:"facilityId"`              // 機構ID
	ProfessionalId          uint64 `json:"professionalId"`          // 專業人士ID
	ProfessionalUserId      uint64 `json:"professionalUserId"`      // 專業人士UserID
	ProfessionalPhotoFileId string `json:"professionalPhotoFileId"` // 專業人士照片文件ID
	LastMessageTime         string `json:"lastMessageTime"`         // 最後消息時間
	LastMessageType         string `json:"lastMessageType"`         // 最後消息類型
	LastMessageContent      string `json:"lastMessageContent"`      // 最後消息內容
	FacilityUnreadCount     int32  `json:"facilityUnreadCount"`     // 機構未讀消息數
	ProfessionalUnreadCount int32  `json:"professionalUnreadCount"` // 專業人士未讀消息數
	FacilityName            string `json:"facilityName"`            // 機構名稱
	ProfessionalName        string `json:"professionalName"`        // 專業人士名稱
}

type WsSessionListByProfessionalResp struct {
	SessionUuid        string `json:"sessionUuid"`        // 聊天會話UUID
	FacilityId         uint64 `json:"facilityId"`         // 機構ID
	FacilityName       string `json:"facilityName"`       // 機構名稱
	LastMessageTime    string `json:"lastMessageTime"`    // 最後消息時間
	LastMessageType    string `json:"lastMessageType"`    // 最後消息類型
	LastMessageContent string `json:"lastMessageContent"` // 最後消息內容
	UnreadCount        int32  `json:"unreadCount"`        // 未讀消息數
	Online             string `json:"online"`             // 在線狀態
}

type WsSessionListByFacilityResp struct {
	SessionUuid             string `json:"sessionUuid"`             // 聊天會話UUID
	ProfessionalId          uint64 `json:"professionalId"`          // 專業人士ID
	ProfessionalUserId      uint64 `json:"professionalUserId"`      // 專業人士UserID
	ProfessionalName        string `json:"professionalName"`        // 專業人士名稱
	ProfessionalPhotoFileId string `json:"professionalPhotoFileId"` // 專業人士照片文件ID
	LastMessageTime         string `json:"lastMessageTime"`         // 最後消息時間
	LastMessageType         string `json:"lastMessageType"`         // 最後消息類型
	LastMessageContent      string `json:"lastMessageContent"`      // 最後消息內容
	UnreadCount             int32  `json:"unreadCount"`             // 未讀消息數
	Online                  string `json:"online"`                  // 在線狀態
}

// 獲取聊天會話列表
func (s wsMessageService) SessionList(db *gorm.DB, req WsSessionListReq) ([]WsSessionListResult, error) {
	var results []WsSessionListResult
	builder := db.Model(&model.WsSessionView{}).
		Table("ws_session_view AS sv").
		Joins("JOIN ws_session AS s ON sv.session_id = s.id").
		Joins("JOIN facility_profile AS fp ON s.facility_id = fp.facility_id AND fp.data_type = ?", model.FacilityProfileDataTypeApproved).
		Joins("JOIN job_application AS ja ON ja.user_id = s.professional_user_id AND ja.facility_id = s.facility_id AND ja.status != ?", model.JobApplicationStatusWithdraw).
		Joins("JOIN professional AS p ON ja.user_id = p.user_id AND p.data_type = ?", model.ProfessionalDataTypeApproved).
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_id = p.id").
		Joins("JOIN professional_file AS pf ON pfr.professional_file_id = pf.id AND pf.user_id = p.user_id AND pf.file_code = ?", model.ProfessionalFileCodePhoto).
		Select([]string{
			"s.id AS session_id",
			"s.session_uuid",
			"s.facility_id",
			"s.professional_user_id",
			"p.id AS professional_id",
			"s.last_message_time",
			"s.last_message_type",
			"s.last_message_content",
			"s.professional_unread_count",
			"s.facility_unread_count",
			"fp.name AS facility_name",
			"CONCAT(p.first_name, ' ', p.last_name) AS professional_name",
			"pfr.professional_file_id AS professional_photo_file_id",
		})
	if req.BeforeTime != "" {
		builder = builder.Where("s.last_message_time < ?", req.BeforeTime)
	}

	if req.ReqUserType == model.WsMessageSenderTypeProfessional {
		builder = builder.Where("sv.professional_user_id = ?", req.ReqUserId)
	} else {
		builder = builder.Where("sv.facility_id = ?", req.FacilityId)
	}

	if req.ReqUserType == model.WsMessageSenderTypeProfessional {
		if req.SearchKeyword != "" {
			builder = builder.Where("fp.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.SearchKeyword))
		}
	} else {
		if req.SearchKeyword != "" {
			builder = builder.Where("CONCAT(p.first_name, ' ', p.last_name) LIKE ?", xgorm.EscapeLikeWithWildcards(req.SearchKeyword))
		}
	}

	if req.CheckBlacklist {
		builder = builder.Joins("LEFT JOIN facility_blacklist AS fb ON sv.facility_id = fb.facility_id AND sv.professional_user_id = fb.user_id").
			Where("fb.id IS NULL")
	}

	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	err := builder.
		Group("s.id").
		Order("sv.last_interact_time DESC").
		Order("s.last_message_time DESC").
		Order("s.id DESC").
		Find(&results).Error

	return results, err
}

// 獲取專業人士聊天會話列表
func (s wsMessageService) ProfessionalSessionList(db *gorm.DB, req WsSessionListReq) ([]WsSessionListByProfessionalResp, error) {
	results, err := s.SessionList(db, req)
	if err != nil {
		return nil, err
	}
	resp := make([]WsSessionListByProfessionalResp, 0)
	for _, result := range results {
		isOnline := s.CheckWsOnlineConn(result.FacilityId, model.WsMessageSenderTypeFacility)
		online := "N"
		if isOnline {
			online = "Y"
		}
		resp = append(resp, WsSessionListByProfessionalResp{
			SessionUuid:        result.SessionUuid,
			FacilityId:         result.FacilityId,
			FacilityName:       result.FacilityName,
			LastMessageTime:    result.LastMessageTime,
			LastMessageType:    result.LastMessageType,
			LastMessageContent: result.LastMessageContent,
			UnreadCount:        result.ProfessionalUnreadCount,
			Online:             online,
		})
	}
	return resp, nil
}

// 獲取機構聊天會話列表
func (s wsMessageService) FacilitySessionList(db *gorm.DB, req WsSessionListReq) ([]WsSessionListByFacilityResp, error) {
	req.CheckBlacklist = true
	results, err := s.SessionList(db, req)
	if err != nil {
		return nil, err
	}
	resp := make([]WsSessionListByFacilityResp, 0)
	for _, result := range results {
		isOnline := s.CheckWsOnlineConn(result.ProfessionalUserId, model.WsMessageSenderTypeProfessional)
		online := "N"
		if isOnline {
			online = "Y"
		}
		resp = append(resp, WsSessionListByFacilityResp{
			SessionUuid:             result.SessionUuid,
			ProfessionalId:          result.ProfessionalId,
			ProfessionalUserId:      result.ProfessionalUserId,
			ProfessionalName:        result.ProfessionalName,
			ProfessionalPhotoFileId: result.ProfessionalPhotoFileId,
			LastMessageTime:         result.LastMessageTime,
			LastMessageType:         result.LastMessageType,
			LastMessageContent:      result.LastMessageContent,
			UnreadCount:             result.FacilityUnreadCount,
			Online:                  online,
		})
	}
	return resp, nil
}

// endregion ---------------------------------------------------- Session List ----------------------------------------------------

// region ---------------------------------------------------- Message List ----------------------------------------------------

// 聊天會話消息列表請求
type WsMessageListReq struct {
	SessionUuid string `form:"sessionUuid" binding:"required"` // 聊天會話UUID
	BeforeTime  string `form:"beforeTime"`                     // 開始時間(UTC)
	Limit       int    `form:"limit"`                          // 限制數量(最大50)
}

type WsMessageResp struct {
	SessionUuid        string      `json:"sessionUuid" gorm:"-"`       // 聊天會話UUID
	MessageUuid        string      `json:"messageUuid"`                // 消息UUID
	RelatedMessageUuid string      `json:"relatedMessageUuid"`         // 相關消息UUID
	MessageType        string      `json:"messageType"`                // 消息類型
	SenderId           uint64      `json:"senderId"`                   // 發送者ID
	SenderType         string      `json:"senderType"`                 // 發送者類型
	Content            string      `json:"content,omitempty"`          // 消息內容
	JobId              uint64      `json:"jobId,omitempty"`            // 工作ID
	JobApplicationId   uint64      `json:"jobApplicationId,omitempty"` // 工作申請ID
	Status             string      `json:"status"`                     // 消息狀態
	Processed          string      `json:"processed"`                  // 是否已處理
	ProcessResult      string      `json:"processResult,omitempty"`    // 用戶處理結果
	CreateTime         string      `json:"createTime"`                 // 創建時間
	Extra              interface{} `json:"extra,omitempty" gorm:"-"`   // 附加資料
}

// 獲取聊天會話消息列表
func (s wsMessageService) MessageList(db *gorm.DB, req WsMessageListReq) ([]WsMessageResp, error) {
	var err error
	var wsSession model.WsSession
	err = db.Where("session_uuid = ?", req.SessionUuid).First(&wsSession).Error
	if err != nil {
		return nil, err
	}

	var resp []WsMessageResp
	builder := db.Model(&model.WsMessage{}).
		Where("status != ?", model.WsMessageStatusDeleted).
		Where("session_id = ?", wsSession.Id)

	if req.BeforeTime != "" {
		builder = builder.Where("create_time < ?", req.BeforeTime)
	}

	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}

	builder = builder.Order("create_time DESC").
		Order("id DESC")

	if err = db.Table("(?) AS wm", builder).
		Select([]string{
			"wm.message_uuid",
			"wm.related_message_uuid",
			"wm.message_type",
			"wm.sender_id",
			"wm.sender_type",
			"wm.content",
			"wm.job_id",
			"wm.job_application_id",
			"wm.status",
			"wm.processed",
			"wm.process_result",
			"wm.create_time",
		}).
		Order("wm.create_time ASC").
		Order("wm.id ASC").
		Scan(&resp).Error; err != nil {
		return nil, err
	}

	jobApplicationIds := make([]uint64, 0)
	for _, v := range resp {
		if v.JobApplicationId > 0 {
			jobApplicationIds = append(jobApplicationIds, v.JobApplicationId)
		}
	}

	jobApplicationIds = xtool.Uint64ArrayDeduplication(jobApplicationIds)
	if len(jobApplicationIds) == 0 {
		return resp, nil
	}

	var jobApplicationMap map[uint64]JobApplicationDetailBySessionResp
	jobApplicationMap, err = JobApplicationService.GetJobApplicationDetailList(db, jobApplicationIds)
	if err != nil {
		return nil, err
	}

	for i := range resp {
		resp[i].SessionUuid = wsSession.SessionUuid
		if v, ok := jobApplicationMap[resp[i].JobApplicationId]; ok {
			resp[i].Extra = v
		}
	}

	return resp, err
}

// endregion ---------------------------------------------------- Message List ----------------------------------------------------

// region ---------------------------------------------------- Unread Count ----------------------------------------------------

type UnreadCountReq struct {
	UserType   string `form:"-" json:"-"` // 用戶類型
	UserId     uint64 `form:"-" json:"-"` // 用戶ID
	FacilityId uint64 `form:"-" json:"-"` // 機構ID
}

type UnreadCountResp struct {
	UnreadCount int `json:"unreadCount"` // 未讀消息數量
}

// 獲取未讀消息數量
func (s wsMessageService) UnreadCount(db *gorm.DB, req UnreadCountReq) (UnreadCountResp, error) {
	var resp UnreadCountResp
	builder := db.Model(&model.WsSession{}).Table("ws_session AS ws")

	if req.UserType == model.WsMessageSenderTypeProfessional {
		builder = builder.Where("ws.professional_user_id = ?", req.UserId).
			Select("COALESCE(SUM(ws.professional_unread_count), 0) as unread_count")
	} else {
		builder = builder.Joins("LEFT JOIN facility_blacklist AS fb ON ws.facility_id = fb.facility_id AND ws.professional_user_id = fb.user_id").
			Where("fb.id IS NULL").
			Where("ws.facility_id = ?", req.FacilityId).
			Select("COALESCE(SUM(ws.facility_unread_count), 0) as unread_count")
	}

	if err := builder.Scan(&resp).Error; err != nil {
		return UnreadCountResp{}, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- Unread Count ----------------------------------------------------
